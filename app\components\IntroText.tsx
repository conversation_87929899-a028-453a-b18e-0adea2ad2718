"use client";
import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import Header from './Header';
import Scroll from './Scroll';

export default function IntroText() {
  const creativeRef = useRef<HTMLDivElement>(null);
  const paragraphRef = useRef<HTMLParagraphElement>(null);

  useEffect(() => {
    const creative = creativeRef.current;
    const paragraph = paragraphRef.current;

    if (creative && paragraph) {
      // Set initial state
      gsap.set([creative, paragraph], {
        opacity: 0,
        y: 30,
      });

      // Create timeline for intro text animation
      const tl = gsap.timeline({ delay: 2.2 }); // Start after preloader

      // Animate Creative text with a typewriter effect
      tl.to(creative, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power2.out",
      })
      .to(paragraph, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out",
      }, "-=0.3");

      // Add a subtle scale animation on hover for Creative text
      const handleMouseEnter = () => {
        gsap.to(creative, {
          scale: 1.05,
          duration: 0.3,
          ease: "power2.out",
        });
      };

      const handleMouseLeave = () => {
        gsap.to(creative, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out",
        });
      };

      creative.addEventListener('mouseenter', handleMouseEnter);
      creative.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        creative.removeEventListener('mouseenter', handleMouseEnter);
        creative.removeEventListener('mouseleave', handleMouseLeave);
      };
    }
  }, []);

  return (
    <div className="intro_texts">
      <Header />
      <div ref={creativeRef} className="Creative">Creative</div>
      <p ref={paragraphRef}>
        Our team is committed to delivering innovative and efficient solutions
        tailored to your needs. Let us simplify your challenges and help you
        achieve your goals seamlessly.
      </p>
      <Scroll />
    </div>
  );
}
  