"use client";
import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Image from "next/image";

gsap.registerPlugin(ScrollTrigger);

type Params = {
  title: string;
  heading: string;
  imageSrc?: string; // Optional image prop
  imageAlt?: string; // Optional alt text
};

const Section = ({ title, heading, imageSrc, imageAlt }: Params) => {
  const titleRef = useRef<HTMLSpanElement>(null);
  const headingRef = useRef<HTMLHeadingElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const title = titleRef.current;
    const headingEl = headingRef.current;
    const imageEl = imageRef.current;

    if (title && headingEl) {
      // Set initial state for text elements
      gsap.set([title, headingEl], {
        opacity: 0,
        y: 50,
      });

      // Set initial state for image if it exists
      if (imageEl) {
        gsap.set(imageEl, {
          opacity: 0,
          scale: 0.8,
          rotation: -5,
        });
      }

      // Create timeline for the section animation
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: title.parentElement,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
        },
      });

      // Animate title first, then heading
      tl.to(title, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out",
      })
      .to(headingEl, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out",
      }, "-=0.4"); // Start 0.4s before the previous animation ends

      // Animate image if it exists
      if (imageEl) {
        tl.to(imageEl, {
          opacity: 1,
          scale: 1,
          rotation: 0,
          duration: 1,
          ease: "back.out(1.7)",
        }, "-=0.6")
        // Add a subtle floating animation
        .to(imageEl, {
          y: -10,
          duration: 2,
          ease: "power2.inOut",
          repeat: -1,
          yoyo: true,
        }, "-=0.5");
      }
    }
  }, []);

  return (
    <div className="section">
      <span ref={titleRef} className="section-title">{title}</span>
      <h2 ref={headingRef} className="section-heading">{heading}</h2>
      {imageSrc && (
        <div ref={imageRef} className="section-image">
          <Image
            src={imageSrc}
            alt={imageAlt || "Section Image"}
            width={100}
            height={100}
            priority
          />
        </div>
      )}
    </div>
  );
};

export default Section;
