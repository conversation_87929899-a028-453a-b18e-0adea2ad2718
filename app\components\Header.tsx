"use client";
import { useState, useEffect, useRef } from "react";
import { gsap } from "gsap";
import Link from "next/link";
import Image from "next/image";

export default function Header() {
  const [isNavOpen, setIsNavOpen] = useState(false);
  const logoRef = useRef<HTMLDivElement>(null);
  const menuItemsRef = useRef<HTMLUListElement>(null);

  const toggleNav = () => {
    setIsNavOpen(!isNavOpen);
  };

  useEffect(() => {
    if (isNavOpen) {
      document.body.style.overflowY = "hidden";
      document.documentElement.style.overflowY = "hidden";

      // Animate menu items when opening
      const menuItems = menuItemsRef.current?.querySelectorAll('li');
      if (menuItems) {
        gsap.fromTo(menuItems,
          {
            opacity: 0,
            y: 30,
          },
          {
            opacity: 1,
            y: 0,
            duration: 0.6,
            stagger: 0.1,
            ease: "power2.out",
            delay: 0.2,
          }
        );
      }
    } else {
      document.body.style.overflowY = "auto";
      document.documentElement.style.overflowY = "auto";
    }

    return () => {
      document.body.style.overflowY = "auto";
      document.documentElement.style.overflowY = "auto";
    };
  }, [isNavOpen]);

  // Animate logo on mount
  useEffect(() => {
    const logo = logoRef.current;
    if (logo) {
      gsap.fromTo(logo,
        {
          opacity: 0,
          scale: 0.8,
          rotation: -10,
        },
        {
          opacity: 1,
          scale: 1,
          rotation: 0,
          duration: 1,
          ease: "back.out(1.7)",
          delay: 2.5, // Start after preloader
        }
      );
    }
  }, []);

  return (
    <div>
      <div className="nav">
        <Link href="/">
          <div ref={logoRef} className="logo">
            <Image src="/Assets/Logo.svg" alt="Logo" width={50} height={50} />
          </div>
        </Link>
        <div className="barr" onClick={toggleNav}>
          <Image
            src={isNavOpen ? "/Assets/close.svg" : "/Assets/barr.svg"}
            alt={isNavOpen ? "Close Icon" : "Menu Icon"}
            width={30}
            height={30}
          />
        </div>
      </div>
      <div className={`fullscreen-nav ${isNavOpen ? "open" : ""}`}>
        <ul ref={menuItemsRef}>
          <li>
            <Link href="/">Home</Link>
          </li>
          <li>
            <Link href="/Services">Services</Link>
          </li>
          <li>
            <Link href="/Projects">Projects</Link>
          </li>
          <li>
            <Link href="/Knowledge">Knowledge</Link>
          </li>
          <li>
            <Link href="/Contact">Contact</Link>
          </li>
        </ul>
      </div>
    </div>
  );
}
