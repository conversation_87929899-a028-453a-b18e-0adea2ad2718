"use client";
import { useState, useEffect, useRef } from "react";
import "./styles/hide.css";
import Image from 'next/image';
import Section from "./components/Section";
import Services from './components/Services';
import Projects from './components/Projects';
import Languages from './components/languages';
import Intro from "./components/Intro";


const Home: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true); 
  const preloaderRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    document.body.style.backgroundColor = 'black';

    const timer = setTimeout(() => {
      setIsLoading(false); 

      // Hide the preloader and show the content
      if (preloaderRef.current) {
        preloaderRef.current.style.display = "none"; 
      }

      if (contentRef.current) {
        contentRef.current.style.opacity = "1"; 
        contentRef.current.style.display = "block"; 
      }

      // After content is loaded, reset the background color
      document.body.style.backgroundColor = ''; 
    }, 2000); 

    return () => {
      clearTimeout(timer);
      // Reset the background color on cleanup (if unmounted before timeout)
      document.body.style.backgroundColor = ''; 
    };
  }, []);

  return (
    <div>
      {isLoading && 
       <div id="preloader" ref={preloaderRef}>
          <Image
            src="/Assets/Logo.svg" 
            alt="Loading"
            width={100}
            height={100}
            priority
          />
        </div>}

      <div id="content" ref={contentRef} style={{ opacity: 1, display: 'none'}}>
        
        <Intro/>
        <Section title="What Do We Offer" heading="Our Services" />
        <Services />
        <Section title="Works" heading="Our Projects" />
        <Projects />
        <Section title="Experience" heading="Knowledge" />
        <Languages />
        <br /><br /><br />
      </div>
    </div>
  );
};

export default Home;
