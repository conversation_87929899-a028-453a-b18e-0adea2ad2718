import ProjectCard from "./ProjectCard";

const Projects = () => {
  return (
    <div className="projects">
          <ProjectCard
        title="Easy Gbm"
        description="Education Platform"
        imageSrc="/Assets/Projects/gb.png"
        link="https://gbm.safouabook.shop/General/"
      />
      <ProjectCard
        title="Rofouf Store"
        description="Library"
        imageSrc="/Assets/Projects/rofouf.png"
        link="https://whary.store/"
      />
      <ProjectCard
        title="El-Malaky store"
        description="e-commerce"
        imageSrc="/Assets/Projects/malaky.jpg"
        link="https://shopt.netlify.app/shop/landing"
      />

      <ProjectCard
        title="Electronics store"
        description="e-commerce"
        imageSrc="/Assets/Projects/casque.jpg"
        link="https://whary.netlify.app/new%20folder/home.html"
      />
      <ProjectCard
        title="Melisse education store"
        description="e-commerce"
        imageSrc="/Assets/Projects/melisse.png"
        link="https://melisse-education.com/"
      />
      <ProjectCard
        title="Whary blog"
        description="blog"
        imageSrc="/Assets/Projects/tas3a.gif"
        link="https://abdelmoghith.github.io/template15/New%20folder/tmp15.html"
      />
      <ProjectCard
        title="crowfund"
        description="blog"
        imageSrc="/Assets/Projects/mastercraft.png"
        link="https://crowhary.netlify.app/crow/"
      />

      <ProjectCard
        title="Easy bank"
        description="Presentation"
        imageSrc="/Assets/Projects/easybank.png"
        link="https://easybankcss.netlify.app/easybank/"
      />
      <ProjectCard
        title="Blogr Apps"
        description="Presentation"
        imageSrc="/Assets/Projects/blogr.png"
        link="https://blogr-whary.netlify.app/blogr/"
      />
      <ProjectCard
        title="Loop Studio"
        description="blog"
        imageSrc="/Assets/Projects/loop.png"
        link="https://dainty-kleicha-2d917b.netlify.app/page/page.html"
      />
      <ProjectCard
        title="Sunny Website"
        description="blog"
        imageSrc="/Assets/Projects/sunny.jpg"
        link="https://sunnywhry.netlify.app/sunny/"
      />
      <ProjectCard
        title="Aola store"
        description="e-commerce"
        imageSrc="/Assets/Projects/aola.png"
        link="https://whary-atelier.netlify.app/aola/"
      />
      <ProjectCard
        title="Copy right"
        description="blog"
        imageSrc="/Assets/Projects/copy.png"
        link="https://abdelmoghith.github.io/template18/New%20folder/temp18.html"
      />
      <ProjectCard
        title="Fylo"
        description="blog"
        imageSrc="/Assets/Projects/fylo.png"
        link="https://abdelmoghith.github.io/template21/New%20folder/tmp21.html"
      />
      <ProjectCard
        title="Currency"
        description="Small Presentation"
        imageSrc="/Assets/Projects/currency.jpg"
        link="https://abdelmoghith.github.io/template21/New%20folder/tmp21.html"
      />
    </div>
  );
};

export default Projects;
