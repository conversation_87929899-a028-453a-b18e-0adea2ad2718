import { motion, useInView } from "framer-motion";
import Image from "next/image";
import { useRef } from "react";

const Services = () => {
  // Create refs for each section
  const websiteRef = useRef(null);
  const mobileRef = useRef(null);
  const desktopRef = useRef(null);

  // Set up intersection observer hooks
  const websiteInView = useInView(websiteRef, { once: true, margin: "-20%" });
  const mobileInView = useInView(mobileRef, { once: true, margin: "-20%" });
  const desktopInView = useInView(desktopRef, { once: true, margin: "-20%" });

  // Animation variants
  const containerVariants = {
    hidden: { 
      opacity: 0,
      y: 100,
    },
    visible: { 
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.1, 0.25, 1],
        staggerChildren: 0.2,
      }
    }
  };

  const imageVariants = {
    hidden: {
      opacity: 0,
      scale: 0.8,
      rotateY: 45
    },
    visible: {
      opacity: 1,
      scale: 1,
      rotateY: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
        type: "spring",
        stiffness: 100
      }
    }
  };

  return (
    <div className="Services">
      <motion.div 
        className=""
        ref={websiteRef}
        initial="hidden"
        animate={websiteInView ? "visible" : "hidden"}
        variants={containerVariants}
      >
        <h3>Websites</h3>
        <motion.div 
          className="service" 
          id="web"
          variants={imageVariants}
        >
          <Image
            src="/Assets/Websites.svg"
            alt="Loading"
            width={350}
            height={350}
            priority
          />
        </motion.div>
      </motion.div>

      <motion.div 
        className=""
        ref={mobileRef}
        initial="hidden"
        animate={mobileInView ? "visible" : "hidden"}
        variants={containerVariants}
      >
        <h3>Mobile Application</h3>
        <motion.div 
          className="service" 
          id="app"
          variants={imageVariants}
        >
          <Image
            src="/Assets/Mobile.svg"
            alt="Loading"
            width={350}
            height={350}
            priority
          />
        </motion.div>
      </motion.div>

      <motion.div 
        className=""
        ref={desktopRef}
        initial="hidden"
        animate={desktopInView ? "visible" : "hidden"}
        variants={containerVariants}
      >
        <h3>Desktop Application</h3>
        <motion.div 
          className="service" 
          id="desk"
          variants={imageVariants}
        >
          <Image
            src="/Assets/Desktop.svg"
            alt="Loading"
            width={350}
            height={350}
            priority
          />
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Services;