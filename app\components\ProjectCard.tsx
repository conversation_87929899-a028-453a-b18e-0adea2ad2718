import Image from "next/image";
import { motion } from "framer-motion";

type Data = {
  link: string;
  title: string;
  description: string;
  imageSrc: string;
};

const ProjectCard = ({ link, title, description, imageSrc }: Data) => (
  <motion.a 
    href={link}
    target="_blank"
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.5 }}
  >
    <div className="box">
      <p>{title}</p>
      <div className="box_Desctription">
        <p>{description}</p>
      </div>
      <div className="multiple">
        <div id="box_1"></div>
        <div id="box_2"></div>
        <div className="box_3">
          <Image
            src={imageSrc}
            alt='Projects'
            layout="fill"
            objectFit="cover"
            priority
          />
        </div>
        <div id="direction"></div>
      </div>
    </div>
  </motion.a>
);

export default ProjectCard;