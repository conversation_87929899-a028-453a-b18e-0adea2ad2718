:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: light) {
  html {
    color-scheme: light;
  }
}
@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #ffffff;
    --text-color: #000000;
  }
}