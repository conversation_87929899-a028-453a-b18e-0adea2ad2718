"use client";
import { useState, useEffect, useRef } from "react";
import "../styles/hide.css";
import Contact_intro from "../components/Contact_intro";
import Section from "../components/Section";
import Image from 'next/image';


const Home: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true); 
  const preloaderRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false); 

      if (preloaderRef.current) {
        preloaderRef.current.style.display = "none"; 
      }

      if (contentRef.current) {
        contentRef.current.style.opacity = "1"; 
        contentRef.current.style.display = "block"; 
      }
    }, 2000); 

    return () => clearTimeout(timer); 
  }, []);

  return (
    <div>

      {isLoading && 
       <div id="preloader" ref={preloaderRef}>
          <Image
            src="/Assets/Logo.svg" 
            alt="Loading"
            width={100}
            height={100}
            priority
          />
        </div>}

      <div id="content" ref={contentRef} style={{ opacity: 1, display: 'none' }}>
        <Contact_intro />
        <Section title="You can" heading="Call Us" />
        <br /><br /><br />
      </div>
    </div>
  );
};

export default Home;
